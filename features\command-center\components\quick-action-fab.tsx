import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Pressable,
  Fab,
  FabIcon,
  FabLabel,
  Modal,
  ModalBackdrop,
  ModalContent,
  ModalHeader,
  ModalCloseButton,
  ModalBody,
  Button,
  ButtonText,
  Input,
  InputField,
} from '@gluestack-ui/themed';
import { useTranslation } from 'react-i18next';
import { useTaskStore } from '@/lib/store/task-store';

export function QuickActionFAB() {
  const { t } = useTranslation();
  const [showModal, setShowModal] = useState(false);
  const [quickTopic, setQuickTopic] = useState('');
  const { generateAITasks, addTask, isGenerating } = useTaskStore();

  const handleQuickAI = async () => {
    if (!quickTopic.trim()) return;
    
    setShowModal(false);
    await generateAITasks(quickTopic);
    setQuickTopic('');
  };

  const handleQuickTask = () => {
    if (!quickTopic.trim()) return;
    
    addTask({
      title: quickTopic,
      description: '手动创建的任务',
      completed: false,
      points: 10,
      isAIGenerated: false,
    });
    
    setShowModal(false);
    setQuickTopic('');
  };

  return (
    <>
      <Fab
        size="lg"
        placement="bottom right"
        bg="$primary500"
        $dark-bg="$primary600"
        onPress={() => setShowModal(true)}
      >
        <FabIcon as={() => <Text fontSize="$xl">⚡</Text>} />
        <FabLabel>快速创建</FabLabel>
      </Fab>

      <Modal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        size="md"
      >
        <ModalBackdrop />
        <ModalContent>
          <ModalHeader>
            <Text fontWeight="$bold" fontSize="$lg">
              快速创建任务
            </Text>
            <ModalCloseButton onPress={() => setShowModal(false)} />
          </ModalHeader>
          
          <ModalBody>
            <VStack space="md" pb="$4">
              <Input>
                <InputField
                  placeholder="输入任务主题或想法..."
                  value={quickTopic}
                  onChangeText={setQuickTopic}
                  autoFocus
                />
              </Input>
              
              <VStack space="sm">
                <Button
                  onPress={handleQuickAI}
                  isDisabled={!quickTopic.trim() || isGenerating}
                  bg="$primary500"
                  $dark-bg="$primary600"
                >
                  <ButtonText>🤖 AI 智能规划</ButtonText>
                </Button>
                
                <Button
                  onPress={handleQuickTask}
                  isDisabled={!quickTopic.trim()}
                  variant="outline"
                  borderColor="$primary500"
                  $dark-borderColor="$primary400"
                >
                  <ButtonText color="$primary500" $dark-color="$primary400">
                    ✏️ 手动添加任务
                  </ButtonText>
                </Button>
              </VStack>
              
              <Text
                size="sm"
                color="$textLight500"
                $dark-color="$textDark400"
                textAlign="center"
              >
                AI 智能规划会生成多个相关任务，手动添加只创建一个任务
              </Text>
            </VStack>
          </ModalBody>
        </ModalContent>
      </Modal>
    </>
  );
}
