import React, { useEffect } from "react";
import { ScrollView } from "react-native";
import { Box, VStack, Text, Heading } from "@gluestack-ui/themed";
import { useTranslation } from "react-i18next";
import { useUserStore } from "@/lib/store/user-store";
import { HeroSection } from "./hero-section";
import { AITaskConsole } from "./ai-task-console";
import { TaskList } from "./task-list";
import { AchievementOverview } from "./achievement-overview";
import { QuickActionFAB } from "./quick-action-fab";

export function CommandCenterScreen() {
  const { t } = useTranslation();
  const initializeAchievements = useUserStore(
    (state) => state.initializeAchievements
  );

  useEffect(() => {
    initializeAchievements();
  }, [initializeAchievements]);

  return (
    <Box flex={1} bg="$backgroundLight0" $dark-bg="$backgroundDark950">
      <ScrollView showsVerticalScrollIndicator={false}>
        <VStack space="lg" p="$4">
          {/* Hero Section */}
          <HeroSection />

          {/* AI Task Console */}
          <AITaskConsole />

          {/* Task List */}
          <TaskList />

          {/* Achievement Overview */}
          <AchievementOverview />
        </VStack>
      </ScrollView>

      {/* Quick Action FAB */}
      <QuickActionFAB />
    </Box>
  );
}
