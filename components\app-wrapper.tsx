import React, { useEffect } from 'react';
import { Box, VStack, Spinner, Text } from '@gluestack-ui/themed';
import { useAuthStore } from '@/lib/store/auth-store';
import { useTaskStore } from '@/lib/store/task-store';
import { useUserStore } from '@/lib/store/user-store';
import { AuthScreen } from '@/features/auth/components/auth-screen';

interface AppWrapperProps {
  children: React.ReactNode;
}

export function AppWrapper({ children }: AppWrapperProps) {
  const { user, initialized, initialize } = useAuthStore();
  const { loadTasks } = useTaskStore();
  const { loadAchievements } = useUserStore();

  useEffect(() => {
    initialize();
  }, [initialize]);

  useEffect(() => {
    if (user) {
      // Load user data when authenticated
      loadTasks();
      loadAchievements();
    }
  }, [user, loadTasks, loadAchievements]);

  // Show loading screen while initializing
  if (!initialized) {
    return (
      <Box flex={1} justifyContent="center" alignItems="center" bg="$backgroundLight0" $dark-bg="$backgroundDark950">
        <VStack space="md" alignItems="center">
          <Spinner size="large" color="$primary500" />
          <Text color="$textLight600" $dark-color="$textDark400">
            初始化应用...
          </Text>
        </VStack>
      </Box>
    );
  }

  // Show auth screen if not authenticated
  if (!user) {
    return <AuthScreen />;
  }

  // Show main app if authenticated
  return <>{children}</>;
}
