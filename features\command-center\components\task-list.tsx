import React from "react";
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Pressable,
  Checkbox,
  CheckboxIndicator,
  CheckboxIcon,
  CheckIcon,
} from "@gluestack-ui/themed";
import { useTranslation } from "react-i18next";
import { useTaskStore } from "@/lib/store/task-store";
import { useUserStore } from "@/lib/store/user-store";

export function TaskList() {
  const { t } = useTranslation();
  const { tasks, completeTask } = useTaskStore();
  const { addPoints, updateStreak, unlockAchievement, stats } = useUserStore();

  const handleCompleteTask = async (taskId: string) => {
    const task = tasks.find((t) => t.id === taskId);
    if (task && !task.completed) {
      await completeTask(taskId);
      addPoints(task.points);
      updateStreak();

      // Check for task completion achievements
      const completedTasks = stats.tasksCompleted + 1;
      if (completedTasks === 10) {
        unlockAchievement("task-executor");
      }
    }
  };

  const activeTasks = tasks.filter((task) => !task.completed);
  const completedTasks = tasks.filter((task) => task.completed);

  return (
    <Box
      bg="$backgroundLight50"
      $dark-bg="$backgroundDark900"
      borderRadius="$lg"
      p="$4"
      borderWidth="$1"
      borderColor="$borderLight200"
      $dark-borderColor="$borderDark800"
    >
      <VStack space="md">
        <Heading size="lg" color="$textLight900" $dark-color="$textDark100">
          {t("commandCenter.taskList.title")}
        </Heading>

        {activeTasks.length === 0 && completedTasks.length === 0 ? (
          <Text
            textAlign="center"
            color="$textLight500"
            $dark-color="$textDark400"
            py="$8"
          >
            {t("commandCenter.taskList.empty")}
          </Text>
        ) : (
          <VStack space="sm">
            {/* Active Tasks */}
            {activeTasks.map((task) => (
              <TaskItem
                key={task.id}
                task={task}
                onComplete={() => handleCompleteTask(task.id)}
              />
            ))}

            {/* Completed Tasks */}
            {completedTasks.length > 0 && (
              <VStack space="sm" mt="$4">
                <Text
                  size="sm"
                  fontWeight="$semibold"
                  color="$textLight600"
                  $dark-color="$textDark300"
                >
                  {t("commandCenter.taskList.completed")}
                </Text>
                {completedTasks.map((task) => (
                  <TaskItem
                    key={task.id}
                    task={task}
                    onComplete={() => {}}
                    isCompleted
                  />
                ))}
              </VStack>
            )}
          </VStack>
        )}
      </VStack>
    </Box>
  );
}

interface TaskItemProps {
  task: any;
  onComplete: () => void;
  isCompleted?: boolean;
}

function TaskItem({ task, onComplete, isCompleted = false }: TaskItemProps) {
  return (
    <Pressable onPress={isCompleted ? undefined : onComplete}>
      <Box
        bg={isCompleted ? "$backgroundLight100" : "$white"}
        $dark-bg={isCompleted ? "$backgroundDark800" : "$backgroundDark850"}
        borderRadius="$md"
        p="$3"
        borderWidth="$1"
        borderColor="$borderLight200"
        $dark-borderColor="$borderDark700"
        opacity={isCompleted ? 0.7 : 1}
      >
        <HStack space="md" alignItems="flex-start">
          <Checkbox
            value={task.completed}
            isChecked={task.completed}
            onChange={onComplete}
            isDisabled={isCompleted}
            mt="$1"
          >
            <CheckboxIndicator>
              <CheckboxIcon as={CheckIcon} />
            </CheckboxIndicator>
          </Checkbox>

          <VStack flex={1} space="xs">
            <Text
              fontWeight="$semibold"
              color="$textLight900"
              $dark-color="$textDark100"
              textDecorationLine={task.completed ? "line-through" : "none"}
            >
              {task.title}
            </Text>

            {task.description && (
              <Text
                size="sm"
                color="$textLight600"
                $dark-color="$textDark400"
                textDecorationLine={task.completed ? "line-through" : "none"}
              >
                {task.description}
              </Text>
            )}

            <HStack space="sm" alignItems="center">
              <Text
                size="xs"
                color="$primary500"
                $dark-color="$primary400"
                fontWeight="$medium"
              >
                +{task.points} 积分
              </Text>

              {task.isAIGenerated && (
                <Text
                  size="xs"
                  color="$secondary500"
                  $dark-color="$secondary400"
                  fontWeight="$medium"
                >
                  🤖 AI生成
                </Text>
              )}
            </HStack>
          </VStack>
        </HStack>
      </Box>
    </Pressable>
  );
}
