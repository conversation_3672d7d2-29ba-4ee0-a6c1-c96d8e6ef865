import { supabase } from '../supabase';
import { Task, UserStats, Achievement } from '../store/types';

export interface DatabaseTask {
  id: string;
  user_id: string;
  title: string;
  description?: string;
  completed: boolean;
  points: number;
  is_ai_generated: boolean;
  created_at: string;
  completed_at?: string;
}

export interface DatabaseProfile {
  id: string;
  username?: string;
  full_name?: string;
  avatar_url?: string;
  total_points: number;
  current_streak: number;
  longest_streak: number;
  tasks_completed: number;
  ai_tasks_generated: number;
  last_activity_date?: string;
}

export interface DatabaseAchievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  points: number;
}

export interface DatabaseUserAchievement {
  id: string;
  user_id: string;
  achievement_id: string;
  unlocked_at: string;
  achievements: DatabaseAchievement;
}

export class SupabaseService {
  // Auth methods
  static async signUp(email: string, password: string, fullName?: string) {
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    });
    
    if (error) throw error;
    return data;
  }

  static async signIn(email: string, password: string) {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) throw error;
    return data;
  }

  static async signOut() {
    const { error } = await supabase.auth.signOut();
    if (error) throw error;
  }

  static async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser();
    if (error) throw error;
    return user;
  }

  // Profile methods
  static async getProfile(): Promise<UserStats | null> {
    const user = await this.getCurrentUser();
    if (!user) return null;

    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', user.id)
      .single();

    if (error) throw error;
    
    if (!data) return null;

    return {
      totalPoints: data.total_points,
      currentStreak: data.current_streak,
      longestStreak: data.longest_streak,
      tasksCompleted: data.tasks_completed,
      aiTasksGenerated: data.ai_tasks_generated,
      lastActivityDate: data.last_activity_date ? new Date(data.last_activity_date) : undefined,
    };
  }

  static async updateProfile(stats: Partial<UserStats>) {
    const user = await this.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const updateData: Partial<DatabaseProfile> = {};
    
    if (stats.totalPoints !== undefined) updateData.total_points = stats.totalPoints;
    if (stats.currentStreak !== undefined) updateData.current_streak = stats.currentStreak;
    if (stats.longestStreak !== undefined) updateData.longest_streak = stats.longestStreak;
    if (stats.tasksCompleted !== undefined) updateData.tasks_completed = stats.tasksCompleted;
    if (stats.aiTasksGenerated !== undefined) updateData.ai_tasks_generated = stats.aiTasksGenerated;
    if (stats.lastActivityDate !== undefined) updateData.last_activity_date = stats.lastActivityDate.toISOString();

    const { error } = await supabase
      .from('profiles')
      .update(updateData)
      .eq('id', user.id);

    if (error) throw error;
  }

  // Task methods
  static async getTasks(): Promise<Task[]> {
    const user = await this.getCurrentUser();
    if (!user) return [];

    const { data, error } = await supabase
      .from('tasks')
      .select('*')
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) throw error;

    return data.map(this.mapDatabaseTaskToTask);
  }

  static async createTask(task: Omit<Task, 'id' | 'createdAt' | 'completedAt'>): Promise<Task> {
    const user = await this.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { data, error } = await supabase
      .from('tasks')
      .insert({
        user_id: user.id,
        title: task.title,
        description: task.description,
        completed: task.completed,
        points: task.points,
        is_ai_generated: task.isAIGenerated,
      })
      .select()
      .single();

    if (error) throw error;

    return this.mapDatabaseTaskToTask(data);
  }

  static async updateTask(taskId: string, updates: Partial<Task>): Promise<void> {
    const user = await this.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const updateData: Partial<DatabaseTask> = {};
    
    if (updates.title !== undefined) updateData.title = updates.title;
    if (updates.description !== undefined) updateData.description = updates.description;
    if (updates.completed !== undefined) {
      updateData.completed = updates.completed;
      if (updates.completed && updates.completedAt) {
        updateData.completed_at = updates.completedAt.toISOString();
      }
    }
    if (updates.points !== undefined) updateData.points = updates.points;

    const { error } = await supabase
      .from('tasks')
      .update(updateData)
      .eq('id', taskId)
      .eq('user_id', user.id);

    if (error) throw error;
  }

  static async deleteTask(taskId: string): Promise<void> {
    const user = await this.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { error } = await supabase
      .from('tasks')
      .delete()
      .eq('id', taskId)
      .eq('user_id', user.id);

    if (error) throw error;
  }

  // Achievement methods
  static async getAchievements(): Promise<Achievement[]> {
    const { data, error } = await supabase
      .from('achievements')
      .select('*')
      .order('points', { ascending: true });

    if (error) throw error;

    return data.map(achievement => ({
      id: achievement.id,
      title: achievement.title,
      description: achievement.description,
      icon: achievement.icon,
      points: achievement.points,
    }));
  }

  static async getUserAchievements(): Promise<Achievement[]> {
    const user = await this.getCurrentUser();
    if (!user) return [];

    const { data, error } = await supabase
      .from('user_achievements')
      .select(`
        *,
        achievements (*)
      `)
      .eq('user_id', user.id);

    if (error) throw error;

    return data.map(userAchievement => ({
      id: userAchievement.achievements.id,
      title: userAchievement.achievements.title,
      description: userAchievement.achievements.description,
      icon: userAchievement.achievements.icon,
      points: userAchievement.achievements.points,
      unlockedAt: new Date(userAchievement.unlocked_at),
    }));
  }

  static async unlockAchievement(achievementId: string): Promise<void> {
    const user = await this.getCurrentUser();
    if (!user) throw new Error('User not authenticated');

    const { error } = await supabase
      .from('user_achievements')
      .insert({
        user_id: user.id,
        achievement_id: achievementId,
      });

    if (error && error.code !== '23505') { // Ignore duplicate key error
      throw error;
    }
  }

  // Helper methods
  private static mapDatabaseTaskToTask(dbTask: DatabaseTask): Task {
    return {
      id: dbTask.id,
      title: dbTask.title,
      description: dbTask.description,
      completed: dbTask.completed,
      points: dbTask.points,
      isAIGenerated: dbTask.is_ai_generated,
      createdAt: new Date(dbTask.created_at),
      completedAt: dbTask.completed_at ? new Date(dbTask.completed_at) : undefined,
    };
  }
}
