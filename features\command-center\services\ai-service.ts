interface AITask {
  title: string;
  description: string;
  points: number;
}

export class AIService {
  private static readonly OPENAI_API_KEY = process.env.EXPO_PUBLIC_OPENAI_API_KEY;
  private static readonly API_URL = 'https://api.openai.com/v1/chat/completions';

  static async generateTasks(topic: string): Promise<AITask[]> {
    try {
      // 如果没有 API 密钥，使用模拟数据
      if (!this.OPENAI_API_KEY) {
        return this.getMockTasks(topic);
      }

      const prompt = `请为主题"${topic}"生成3个具体可执行的任务。每个任务应该包含：
1. 简洁明确的标题
2. 详细的描述说明
3. 根据任务复杂度给出10-30分的积分

请以JSON格式返回，格式如下：
[
  {
    "title": "任务标题",
    "description": "任务描述",
    "points": 15
  }
]`;

      const response = await fetch(this.API_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.OPENAI_API_KEY}`,
        },
        body: JSON.stringify({
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: '你是一个专业的任务规划助手，擅长将复杂的目标分解为具体可执行的任务。',
            },
            {
              role: 'user',
              content: prompt,
            },
          ],
          max_tokens: 1000,
          temperature: 0.7,
        }),
      });

      if (!response.ok) {
        throw new Error(`OpenAI API error: ${response.status}`);
      }

      const data = await response.json();
      const content = data.choices[0]?.message?.content;

      if (!content) {
        throw new Error('No content received from OpenAI');
      }

      // 尝试解析 JSON 响应
      try {
        const tasks = JSON.parse(content);
        return Array.isArray(tasks) ? tasks : this.getMockTasks(topic);
      } catch (parseError) {
        console.warn('Failed to parse AI response, using mock data:', parseError);
        return this.getMockTasks(topic);
      }
    } catch (error) {
      console.error('AI service error:', error);
      // 如果 AI 服务失败，返回模拟数据
      return this.getMockTasks(topic);
    }
  }

  private static getMockTasks(topic: string): AITask[] {
    return [
      {
        title: `研究 ${topic} 的基础知识`,
        description: `深入了解 ${topic} 的核心概念、原理和基础理论，为后续学习打下坚实基础`,
        points: 15,
      },
      {
        title: `制定 ${topic} 学习计划`,
        description: `根据个人情况和目标，制定详细的学习路线图和时间安排，确保学习的系统性和连续性`,
        points: 20,
      },
      {
        title: `实践 ${topic} 相关技能`,
        description: `通过实际项目或练习来应用所学知识，将理论转化为实际能力`,
        points: 25,
      },
    ];
  }
}
