import React from 'react';
import { Box, VStack, HStack, Text, Heading } from '@gluestack-ui/themed';
import { useTranslation } from 'react-i18next';
import { useUserStore } from '@/lib/store/user-store';

export function HeroSection() {
  const { t } = useTranslation();
  const stats = useUserStore((state) => state.stats);

  return (
    <Box
      bg="$primary500"
      $dark-bg="$primary600"
      borderRadius="$xl"
      p="$6"
    >
      <VStack space="md">
        {/* Greeting */}
        <Heading size="xl" color="$white">
          {t('commandCenter.greeting')}
        </Heading>
        
        {/* Stats */}
        <HStack space="lg" justifyContent="space-between">
          <VStack alignItems="center">
            <Text size="2xl" fontWeight="$bold" color="$white">
              {stats.totalPoints}
            </Text>
            <Text size="sm" color="$primary100">
              {t('commandCenter.stats.totalPoints')}
            </Text>
          </VStack>
          
          <VStack alignItems="center">
            <Text size="2xl" fontWeight="$bold" color="$white">
              {stats.currentStreak}
            </Text>
            <Text size="sm" color="$primary100">
              {t('commandCenter.stats.currentStreak')}
            </Text>
          </VStack>
          
          <VStack alignItems="center">
            <Text size="2xl" fontWeight="$bold" color="$white">
              {stats.tasksCompleted}
            </Text>
            <Text size="sm" color="$primary100">
              已完成任务
            </Text>
          </VStack>
        </HStack>
      </VStack>
    </Box>
  );
}
