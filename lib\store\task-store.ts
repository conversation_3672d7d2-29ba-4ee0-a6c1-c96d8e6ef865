import { create } from 'zustand';
import { Task } from './types';

interface TaskState {
  tasks: Task[];
  isGenerating: boolean;
  addTask: (task: Omit<Task, 'id' | 'createdAt'>) => void;
  completeTask: (taskId: string) => void;
  removeTask: (taskId: string) => void;
  setGenerating: (isGenerating: boolean) => void;
  generateAITasks: (topic: string) => Promise<void>;
}

export const useTaskStore = create<TaskState>((set, get) => ({
  tasks: [],
  isGenerating: false,

  addTask: (taskData) => {
    const newTask: Task = {
      ...taskData,
      id: Date.now().toString(),
      createdAt: new Date(),
    };
    set((state) => ({
      tasks: [...state.tasks, newTask],
    }));
  },

  completeTask: (taskId) => {
    set((state) => ({
      tasks: state.tasks.map((task) =>
        task.id === taskId
          ? { ...task, completed: true, completedAt: new Date() }
          : task
      ),
    }));
  },

  removeTask: (taskId) => {
    set((state) => ({
      tasks: state.tasks.filter((task) => task.id !== taskId),
    }));
  },

  setGenerating: (isGenerating) => {
    set({ isGenerating });
  },

  generateAITasks: async (topic) => {
    set({ isGenerating: true });
    
    try {
      // Simulate AI task generation
      await new Promise((resolve) => setTimeout(resolve, 2000));
      
      // Mock AI generated tasks
      const aiTasks = [
        {
          title: `研究 ${topic} 的基础知识`,
          description: `深入了解 ${topic} 的核心概念和原理`,
          completed: false,
          points: 10,
          isAIGenerated: true,
        },
        {
          title: `制定 ${topic} 学习计划`,
          description: `创建详细的学习路线图和时间安排`,
          completed: false,
          points: 15,
          isAIGenerated: true,
        },
        {
          title: `实践 ${topic} 相关技能`,
          description: `通过实际项目应用所学知识`,
          completed: false,
          points: 20,
          isAIGenerated: true,
        },
      ];

      const { addTask } = get();
      aiTasks.forEach((task) => addTask(task));
    } catch (error) {
      console.error('Failed to generate AI tasks:', error);
    } finally {
      set({ isGenerating: false });
    }
  },
}));
