import { create } from "zustand";
import { Task } from "./types";
import { AIService } from "@/features/command-center/services/ai-service";
import { SupabaseService } from "../services/supabase-service";

interface TaskState {
  tasks: Task[];
  isGenerating: boolean;
  loading: boolean;
  addTask: (task: Omit<Task, "id" | "createdAt">) => Promise<void>;
  completeTask: (taskId: string) => Promise<void>;
  removeTask: (taskId: string) => Promise<void>;
  setGenerating: (isGenerating: boolean) => void;
  generateAITasks: (topic: string) => Promise<void>;
  loadTasks: () => Promise<void>;
  syncToDatabase: () => Promise<void>;
}

export const useTaskStore = create<TaskState>((set, get) => ({
  tasks: [],
  isGenerating: false,
  loading: false,

  addTask: async (taskData) => {
    try {
      // Try to create in database first
      const newTask = await SupabaseService.createTask(taskData);
      set((state) => ({
        tasks: [...state.tasks, newTask],
      }));
    } catch (error) {
      console.error("Failed to create task in database:", error);
      // Fallback to local storage
      const newTask: Task = {
        ...taskData,
        id: Date.now().toString(),
        createdAt: new Date(),
      };
      set((state) => ({
        tasks: [...state.tasks, newTask],
      }));
    }
  },

  completeTask: async (taskId) => {
    const completedAt = new Date();

    // Update local state immediately for better UX
    set((state) => ({
      tasks: state.tasks.map((task) =>
        task.id === taskId ? { ...task, completed: true, completedAt } : task
      ),
    }));

    // Sync to database
    try {
      await SupabaseService.updateTask(taskId, {
        completed: true,
        completedAt,
      });
    } catch (error) {
      console.error("Failed to update task in database:", error);
      // Could implement retry logic or offline queue here
    }
  },

  removeTask: async (taskId) => {
    // Update local state immediately
    set((state) => ({
      tasks: state.tasks.filter((task) => task.id !== taskId),
    }));

    // Sync to database
    try {
      await SupabaseService.deleteTask(taskId);
    } catch (error) {
      console.error("Failed to delete task from database:", error);
      // Could implement retry logic here
    }
  },

  setGenerating: (isGenerating) => {
    set({ isGenerating });
  },

  generateAITasks: async (topic) => {
    set({ isGenerating: true });

    try {
      const aiTasks = await AIService.generateTasks(topic);

      const { addTask } = get();
      for (const task of aiTasks) {
        await addTask({
          ...task,
          completed: false,
          isAIGenerated: true,
        });
      }
    } catch (error) {
      console.error("Failed to generate AI tasks:", error);
    } finally {
      set({ isGenerating: false });
    }
  },

  loadTasks: async () => {
    set({ loading: true });
    try {
      const tasks = await SupabaseService.getTasks();
      set({ tasks });
    } catch (error) {
      console.error("Failed to load tasks:", error);
    } finally {
      set({ loading: false });
    }
  },

  syncToDatabase: async () => {
    try {
      // This method can be used for batch sync if needed
      // For now, individual operations handle their own sync
    } catch (error) {
      console.error("Failed to sync tasks to database:", error);
    }
  },
}));
