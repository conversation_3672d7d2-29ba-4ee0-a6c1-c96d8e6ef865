import { create } from "zustand";
import { Task } from "./types";
import { AIService } from "@/features/command-center/services/ai-service";

interface TaskState {
  tasks: Task[];
  isGenerating: boolean;
  addTask: (task: Omit<Task, "id" | "createdAt">) => void;
  completeTask: (taskId: string) => void;
  removeTask: (taskId: string) => void;
  setGenerating: (isGenerating: boolean) => void;
  generateAITasks: (topic: string) => Promise<void>;
}

export const useTaskStore = create<TaskState>((set, get) => ({
  tasks: [],
  isGenerating: false,

  addTask: (taskData) => {
    const newTask: Task = {
      ...taskData,
      id: Date.now().toString(),
      createdAt: new Date(),
    };
    set((state) => ({
      tasks: [...state.tasks, newTask],
    }));
  },

  completeTask: (taskId) => {
    set((state) => ({
      tasks: state.tasks.map((task) =>
        task.id === taskId
          ? { ...task, completed: true, completedAt: new Date() }
          : task
      ),
    }));
  },

  removeTask: (taskId) => {
    set((state) => ({
      tasks: state.tasks.filter((task) => task.id !== taskId),
    }));
  },

  setGenerating: (isGenerating) => {
    set({ isGenerating });
  },

  generateAITasks: async (topic) => {
    set({ isGenerating: true });

    try {
      const aiTasks = await AIService.generateTasks(topic);

      const { addTask } = get();
      aiTasks.forEach((task) =>
        addTask({
          ...task,
          completed: false,
          isAIGenerated: true,
        })
      );
    } catch (error) {
      console.error("Failed to generate AI tasks:", error);
    } finally {
      set({ isGenerating: false });
    }
  },
}));
