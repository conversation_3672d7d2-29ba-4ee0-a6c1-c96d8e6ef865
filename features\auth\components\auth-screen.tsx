import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Button,
  ButtonText,
  Input,
  InputField,
  FormControl,
  FormControlLabel,
  FormControlLabelText,
  FormControlError,
  FormControlErrorText,
  Alert,
  AlertIcon,
  AlertText,
  Spinner,
  Pressable,
} from '@gluestack-ui/themed';
import { useTranslation } from 'react-i18next';
import { useAuthStore } from '@/lib/store/auth-store';

export function AuthScreen() {
  const { t } = useTranslation();
  const [isSignUp, setIsSignUp] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [error, setError] = useState('');
  
  const { signIn, signUp, loading } = useAuthStore();

  const handleSubmit = async () => {
    setError('');
    
    if (!email || !password) {
      setError('请填写所有必填字段');
      return;
    }

    if (isSignUp && !fullName) {
      setError('请输入您的姓名');
      return;
    }

    try {
      if (isSignUp) {
        await signUp(email, password, fullName);
      } else {
        await signIn(email, password);
      }
    } catch (err: any) {
      setError(err.message || '操作失败，请重试');
    }
  };

  const toggleMode = () => {
    setIsSignUp(!isSignUp);
    setError('');
    setEmail('');
    setPassword('');
    setFullName('');
  };

  return (
    <Box flex={1} bg="$backgroundLight0" $dark-bg="$backgroundDark950">
      <VStack flex={1} justifyContent="center" p="$6" space="lg">
        {/* Header */}
        <VStack space="sm" alignItems="center">
          <Text fontSize="$6xl">🎯</Text>
          <Heading size="2xl" textAlign="center" color="$textLight900" $dark-color="$textDark100">
            {t('app.name')}
          </Heading>
          <Text textAlign="center" color="$textLight600" $dark-color="$textDark400">
            AI 辅助的智能任务管理
          </Text>
        </VStack>

        {/* Form */}
        <VStack space="md">
          {error ? (
            <Alert action="error" variant="solid">
              <AlertIcon />
              <AlertText>{error}</AlertText>
            </Alert>
          ) : null}

          {isSignUp && (
            <FormControl>
              <FormControlLabel>
                <FormControlLabelText>姓名</FormControlLabelText>
              </FormControlLabel>
              <Input>
                <InputField
                  placeholder="请输入您的姓名"
                  value={fullName}
                  onChangeText={setFullName}
                  autoCapitalize="words"
                />
              </Input>
            </FormControl>
          )}

          <FormControl>
            <FormControlLabel>
              <FormControlLabelText>邮箱</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField
                placeholder="请输入邮箱地址"
                value={email}
                onChangeText={setEmail}
                keyboardType="email-address"
                autoCapitalize="none"
                autoCorrect={false}
              />
            </Input>
          </FormControl>

          <FormControl>
            <FormControlLabel>
              <FormControlLabelText>密码</FormControlLabelText>
            </FormControlLabel>
            <Input>
              <InputField
                placeholder="请输入密码"
                value={password}
                onChangeText={setPassword}
                secureTextEntry
              />
            </Input>
          </FormControl>

          <Button
            onPress={handleSubmit}
            isDisabled={loading}
            bg="$primary500"
            $dark-bg="$primary600"
            mt="$4"
          >
            {loading ? (
              <HStack space="sm" alignItems="center">
                <Spinner size="small" color="$white" />
                <ButtonText color="$white">
                  {isSignUp ? '注册中...' : '登录中...'}
                </ButtonText>
              </HStack>
            ) : (
              <ButtonText color="$white">
                {isSignUp ? '注册账户' : '登录'}
              </ButtonText>
            )}
          </Button>

          {/* Toggle Mode */}
          <HStack justifyContent="center" space="xs" mt="$4">
            <Text color="$textLight600" $dark-color="$textDark400">
              {isSignUp ? '已有账户？' : '还没有账户？'}
            </Text>
            <Pressable onPress={toggleMode}>
              <Text color="$primary500" $dark-color="$primary400" fontWeight="$semibold">
                {isSignUp ? '立即登录' : '立即注册'}
              </Text>
            </Pressable>
          </HStack>
        </VStack>

        {/* Guest Mode */}
        <VStack space="sm" mt="$8">
          <Text textAlign="center" color="$textLight500" $dark-color="$textDark500" size="sm">
            或者
          </Text>
          <Button
            variant="outline"
            borderColor="$borderLight300"
            $dark-borderColor="$borderDark700"
            onPress={() => {
              // TODO: Implement guest mode
              console.log('Guest mode not implemented yet');
            }}
          >
            <ButtonText color="$textLight700" $dark-color="$textDark300">
              以访客身份继续
            </ButtonText>
          </Button>
          <Text textAlign="center" color="$textLight400" $dark-color="$textDark600" size="xs">
            访客模式下数据不会保存
          </Text>
        </VStack>
      </VStack>
    </Box>
  );
}
