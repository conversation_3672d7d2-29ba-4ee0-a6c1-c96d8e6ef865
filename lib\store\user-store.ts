import { create } from "zustand";
import { UserStats, Achievement } from "./types";
import { SupabaseService } from "../services/supabase-service";

interface UserState {
  stats: UserStats;
  achievements: Achievement[];
  loading: boolean;
  addPoints: (points: number) => void;
  updateStreak: () => void;
  unlockAchievement: (achievementId: string) => void;
  initializeAchievements: () => void;
  syncFromDatabase: (stats: UserStats) => void;
  syncToDatabase: () => Promise<void>;
  loadAchievements: () => Promise<void>;
}

const defaultStats: UserStats = {
  totalPoints: 0,
  currentStreak: 0,
  longestStreak: 0,
  tasksCompleted: 0,
  aiTasksGenerated: 0,
};

const defaultAchievements: Achievement[] = [
  {
    id: "ai-explorer",
    title: "AI 初探者",
    description: "首次成功使用 AI 生成 Todo List",
    icon: "🤖",
    points: 50,
  },
  {
    id: "task-executor",
    title: "任务执行者",
    description: "累计完成 10 个 AI 生成的任务",
    icon: "⚡",
    points: 100,
  },
  {
    id: "streak-star",
    title: "连胜新星",
    description: "首次达成 3 天连胜",
    icon: "🌟",
    points: 75,
  },
];

export const useUserStore = create<UserState>((set, get) => ({
  stats: defaultStats,
  achievements: [],
  loading: false,

  addPoints: (points) => {
    set((state) => ({
      stats: {
        ...state.stats,
        totalPoints: state.stats.totalPoints + points,
      },
    }));
  },

  updateStreak: () => {
    const today = new Date();
    const { stats } = get();

    // Check if last activity was yesterday
    const lastActivity = stats.lastActivityDate;
    const isConsecutive =
      lastActivity &&
      Math.abs(today.getTime() - lastActivity.getTime()) <= 24 * 60 * 60 * 1000;

    const newStreak = isConsecutive ? stats.currentStreak + 1 : 1;

    set((state) => ({
      stats: {
        ...state.stats,
        currentStreak: newStreak,
        longestStreak: Math.max(state.stats.longestStreak, newStreak),
        tasksCompleted: state.stats.tasksCompleted + 1,
        lastActivityDate: today,
      },
    }));

    // Check for streak achievements
    if (newStreak === 3) {
      get().unlockAchievement("streak-star");
    }
  },

  unlockAchievement: (achievementId) => {
    set((state) => {
      const achievement = state.achievements.find(
        (a) => a.id === achievementId
      );
      if (achievement && !achievement.unlockedAt) {
        const updatedAchievements = state.achievements.map((a) =>
          a.id === achievementId ? { ...a, unlockedAt: new Date() } : a
        );

        // Add achievement points
        const achievementPoints = achievement.points;

        return {
          achievements: updatedAchievements,
          stats: {
            ...state.stats,
            totalPoints: state.stats.totalPoints + achievementPoints,
          },
        };
      }
      return state;
    });
  },

  initializeAchievements: () => {
    set({ achievements: defaultAchievements });
  },

  syncFromDatabase: (stats) => {
    set({ stats });
  },

  syncToDatabase: async () => {
    try {
      const { stats } = get();
      await SupabaseService.updateProfile(stats);
    } catch (error) {
      console.error("Failed to sync to database:", error);
    }
  },

  loadAchievements: async () => {
    set({ loading: true });
    try {
      const [allAchievements, userAchievements] = await Promise.all([
        SupabaseService.getAchievements(),
        SupabaseService.getUserAchievements(),
      ]);

      // Merge achievements with unlock status
      const mergedAchievements = allAchievements.map((achievement) => {
        const userAchievement = userAchievements.find(
          (ua) => ua.id === achievement.id
        );
        return {
          ...achievement,
          unlockedAt: userAchievement?.unlockedAt,
        };
      });

      set({ achievements: mergedAchievements });
    } catch (error) {
      console.error("Failed to load achievements:", error);
      // Fallback to default achievements
      set({ achievements: defaultAchievements });
    } finally {
      set({ loading: false });
    }
  },
}));
