import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  ScrollView,
  Switch,
  Button,
  ButtonText,
  Divider,
  Pressable,
  AlertDialog,
  AlertDialogBackdrop,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogCloseButton,
  AlertDialogBody,
  AlertDialogFooter,
} from '@gluestack-ui/themed';
import { useTranslation } from 'react-i18next';
import { useTaskStore } from '@/lib/store/task-store';
import { useUserStore } from '@/lib/store/user-store';

export default function SettingsScreen() {
  const { t, i18n } = useTranslation();
  const [showResetDialog, setShowResetDialog] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const tasks = useTaskStore((state) => state.tasks);
  const stats = useUserStore((state) => state.stats);

  const handleLanguageToggle = () => {
    const newLang = i18n.language === 'zh' ? 'en' : 'zh';
    i18n.changeLanguage(newLang);
  };

  const handleResetData = () => {
    // 这里可以添加重置数据的逻辑
    setShowResetDialog(false);
  };

  return (
    <Box flex={1} bg="$backgroundLight0" $dark-bg="$backgroundDark950">
      <ScrollView showsVerticalScrollIndicator={false}>
        <VStack space="lg" p="$4">
          {/* Header */}
          <VStack space="sm">
            <Heading size="2xl" color="$textLight900" $dark-color="$textDark100">
              设置
            </Heading>
            <Text color="$textLight600" $dark-color="$textDark400">
              个性化您的 SupaPlayer 体验
            </Text>
          </VStack>

          {/* Statistics Section */}
          <Box
            bg="$backgroundLight50"
            $dark-bg="$backgroundDark900"
            borderRadius="$lg"
            p="$4"
            borderWidth="$1"
            borderColor="$borderLight200"
            $dark-borderColor="$borderDark800"
          >
            <VStack space="md">
              <Heading size="lg" color="$textLight900" $dark-color="$textDark100">
                使用统计
              </Heading>
              
              <VStack space="sm">
                <HStack justifyContent="space-between">
                  <Text color="$textLight600" $dark-color="$textDark400">
                    总积分
                  </Text>
                  <Text fontWeight="$semibold" color="$textLight900" $dark-color="$textDark100">
                    {stats.totalPoints}
                  </Text>
                </HStack>
                
                <HStack justifyContent="space-between">
                  <Text color="$textLight600" $dark-color="$textDark400">
                    已完成任务
                  </Text>
                  <Text fontWeight="$semibold" color="$textLight900" $dark-color="$textDark100">
                    {stats.tasksCompleted}
                  </Text>
                </HStack>
                
                <HStack justifyContent="space-between">
                  <Text color="$textLight600" $dark-color="$textDark400">
                    当前任务数
                  </Text>
                  <Text fontWeight="$semibold" color="$textLight900" $dark-color="$textDark100">
                    {tasks.filter(t => !t.completed).length}
                  </Text>
                </HStack>
                
                <HStack justifyContent="space-between">
                  <Text color="$textLight600" $dark-color="$textDark400">
                    最长连胜
                  </Text>
                  <Text fontWeight="$semibold" color="$textLight900" $dark-color="$textDark100">
                    {stats.longestStreak} 天
                  </Text>
                </HStack>
              </VStack>
            </VStack>
          </Box>

          {/* Preferences Section */}
          <Box
            bg="$backgroundLight50"
            $dark-bg="$backgroundDark900"
            borderRadius="$lg"
            p="$4"
            borderWidth="$1"
            borderColor="$borderLight200"
            $dark-borderColor="$borderDark800"
          >
            <VStack space="md">
              <Heading size="lg" color="$textLight900" $dark-color="$textDark100">
                偏好设置
              </Heading>
              
              <VStack space="sm">
                <HStack justifyContent="space-between" alignItems="center">
                  <VStack flex={1}>
                    <Text fontWeight="$semibold" color="$textLight900" $dark-color="$textDark100">
                      语言
                    </Text>
                    <Text size="sm" color="$textLight600" $dark-color="$textDark400">
                      当前: {i18n.language === 'zh' ? '中文' : 'English'}
                    </Text>
                  </VStack>
                  <Button
                    size="sm"
                    variant="outline"
                    onPress={handleLanguageToggle}
                  >
                    <ButtonText>切换</ButtonText>
                  </Button>
                </HStack>
                
                <Divider />
                
                <HStack justifyContent="space-between" alignItems="center">
                  <VStack flex={1}>
                    <Text fontWeight="$semibold" color="$textLight900" $dark-color="$textDark100">
                      深色模式
                    </Text>
                    <Text size="sm" color="$textLight600" $dark-color="$textDark400">
                      切换应用主题
                    </Text>
                  </VStack>
                  <Switch
                    value={isDarkMode}
                    onValueChange={setIsDarkMode}
                    trackColor={{ false: '$backgroundLight300', true: '$primary500' }}
                  />
                </HStack>
              </VStack>
            </VStack>
          </Box>

          {/* Data Management Section */}
          <Box
            bg="$backgroundLight50"
            $dark-bg="$backgroundDark900"
            borderRadius="$lg"
            p="$4"
            borderWidth="$1"
            borderColor="$borderLight200"
            $dark-borderColor="$borderDark800"
          >
            <VStack space="md">
              <Heading size="lg" color="$textLight900" $dark-color="$textDark100">
                数据管理
              </Heading>
              
              <VStack space="sm">
                <Button
                  variant="outline"
                  borderColor="$error500"
                  onPress={() => setShowResetDialog(true)}
                >
                  <ButtonText color="$error500">
                    重置所有数据
                  </ButtonText>
                </Button>
                
                <Text size="sm" color="$textLight500" $dark-color="$textDark500">
                  这将删除所有任务、积分和成就数据，此操作不可撤销。
                </Text>
              </VStack>
            </VStack>
          </Box>

          {/* About Section */}
          <Box
            bg="$backgroundLight50"
            $dark-bg="$backgroundDark900"
            borderRadius="$lg"
            p="$4"
            borderWidth="$1"
            borderColor="$borderLight200"
            $dark-borderColor="$borderDark800"
          >
            <VStack space="md">
              <Heading size="lg" color="$textLight900" $dark-color="$textDark100">
                关于
              </Heading>
              
              <VStack space="sm">
                <Text color="$textLight600" $dark-color="$textDark400">
                  SupaPlayer v1.0.0
                </Text>
                <Text size="sm" color="$textLight500" $dark-color="$textDark500">
                  AI 辅助的智能任务管理应用，让您的目标变得更加清晰可执行。
                </Text>
              </VStack>
            </VStack>
          </Box>
        </VStack>
      </ScrollView>

      {/* Reset Confirmation Dialog */}
      <AlertDialog
        isOpen={showResetDialog}
        onClose={() => setShowResetDialog(false)}
      >
        <AlertDialogBackdrop />
        <AlertDialogContent>
          <AlertDialogHeader>
            <Heading size="lg">确认重置</Heading>
            <AlertDialogCloseButton onPress={() => setShowResetDialog(false)} />
          </AlertDialogHeader>
          <AlertDialogBody>
            <Text>
              您确定要重置所有数据吗？这将删除所有任务、积分和成就，此操作不可撤销。
            </Text>
          </AlertDialogBody>
          <AlertDialogFooter>
            <HStack space="sm">
              <Button
                variant="outline"
                onPress={() => setShowResetDialog(false)}
              >
                <ButtonText>取消</ButtonText>
              </Button>
              <Button
                bg="$error500"
                onPress={handleResetData}
              >
                <ButtonText color="$white">确认重置</ButtonText>
              </Button>
            </HStack>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Box>
  );
}
