import React, { useState } from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  Button,
  ButtonText,
  Input,
  InputField,
  Spinner,
} from '@gluestack-ui/themed';
import { useTranslation } from 'react-i18next';
import { useTaskStore } from '@/lib/store/task-store';
import { useUserStore } from '@/lib/store/user-store';

export function AITaskConsole() {
  const { t } = useTranslation();
  const [topic, setTopic] = useState('');
  const { generateAITasks, isGenerating } = useTaskStore();
  const { unlockAchievement, stats } = useUserStore();

  const handleGenerateTasks = async () => {
    if (!topic.trim()) return;
    
    await generateAITasks(topic);
    
    // Unlock AI Explorer achievement on first use
    if (stats.aiTasksGenerated === 0) {
      unlockAchievement('ai-explorer');
    }
    
    setTopic('');
  };

  return (
    <Box
      bg="$backgroundLight50"
      $dark-bg="$backgroundDark900"
      borderRadius="$lg"
      p="$4"
      borderWidth="$1"
      borderColor="$borderLight200"
      $dark-borderColor="$borderDark800"
    >
      <VStack space="md">
        <Heading size="lg" color="$textLight900" $dark-color="$textDark100">
          {t('commandCenter.aiTaskGeneration.title')}
        </Heading>
        
        <VStack space="sm">
          <Input>
            <InputField
              placeholder={t('commandCenter.aiTaskGeneration.placeholder')}
              value={topic}
              onChangeText={setTopic}
              editable={!isGenerating}
            />
          </Input>
          
          <Button
            onPress={handleGenerateTasks}
            isDisabled={!topic.trim() || isGenerating}
            bg="$primary500"
            $dark-bg="$primary600"
          >
            {isGenerating ? (
              <HStack space="sm" alignItems="center">
                <Spinner size="small" color="$white" />
                <ButtonText color="$white">
                  {t('commandCenter.aiTaskGeneration.generating')}
                </ButtonText>
              </HStack>
            ) : (
              <ButtonText color="$white">
                {t('commandCenter.aiTaskGeneration.button')}
              </ButtonText>
            )}
          </Button>
        </VStack>
      </VStack>
    </Box>
  );
}
