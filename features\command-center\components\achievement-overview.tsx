import React from 'react';
import {
  Box,
  VStack,
  HStack,
  Text,
  Heading,
  ScrollView,
} from '@gluestack-ui/themed';
import { useTranslation } from 'react-i18next';
import { useUserStore } from '@/lib/store/user-store';

export function AchievementOverview() {
  const { t } = useTranslation();
  const achievements = useUserStore((state) => state.achievements);

  const unlockedAchievements = achievements.filter(a => a.unlockedAt);
  const recentAchievements = unlockedAchievements
    .sort((a, b) => (b.unlockedAt?.getTime() || 0) - (a.unlockedAt?.getTime() || 0))
    .slice(0, 2);

  const progressAchievements = achievements.filter(a => !a.unlockedAt).slice(0, 2);

  return (
    <Box
      bg="$backgroundLight50"
      $dark-bg="$backgroundDark900"
      borderRadius="$lg"
      p="$4"
      borderWidth="$1"
      borderColor="$borderLight200"
      $dark-borderColor="$borderDark800"
    >
      <VStack space="md">
        <Heading size="lg" color="$textLight900" $dark-color="$textDark100">
          {t('commandCenter.stats.achievements')}
        </Heading>
        
        {recentAchievements.length > 0 && (
          <VStack space="sm">
            <Text
              size="sm"
              fontWeight="$semibold"
              color="$textLight600"
              $dark-color="$textDark300"
            >
              最近解锁
            </Text>
            
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <HStack space="sm">
                {recentAchievements.map((achievement) => (
                  <AchievementCard
                    key={achievement.id}
                    achievement={achievement}
                    isUnlocked
                  />
                ))}
              </HStack>
            </ScrollView>
          </VStack>
        )}
        
        {progressAchievements.length > 0 && (
          <VStack space="sm">
            <Text
              size="sm"
              fontWeight="$semibold"
              color="$textLight600"
              $dark-color="$textDark300"
            >
              进行中
            </Text>
            
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <HStack space="sm">
                {progressAchievements.map((achievement) => (
                  <AchievementCard
                    key={achievement.id}
                    achievement={achievement}
                    isUnlocked={false}
                  />
                ))}
              </HStack>
            </ScrollView>
          </VStack>
        )}
        
        {recentAchievements.length === 0 && progressAchievements.length === 0 && (
          <Text
            textAlign="center"
            color="$textLight500"
            $dark-color="$textDark400"
            py="$4"
          >
            开始完成任务来解锁成就吧！
          </Text>
        )}
      </VStack>
    </Box>
  );
}

interface AchievementCardProps {
  achievement: any;
  isUnlocked: boolean;
}

function AchievementCard({ achievement, isUnlocked }: AchievementCardProps) {
  return (
    <Box
      bg={isUnlocked ? "$success50" : "$backgroundLight100"}
      $dark-bg={isUnlocked ? "$success900" : "$backgroundDark800"}
      borderRadius="$md"
      p="$3"
      borderWidth="$1"
      borderColor={isUnlocked ? "$success200" : "$borderLight200"}
      $dark-borderColor={isUnlocked ? "$success700" : "$borderDark700"}
      minWidth={120}
      opacity={isUnlocked ? 1 : 0.6}
    >
      <VStack space="xs" alignItems="center">
        <Text size="2xl">{achievement.icon}</Text>
        
        <Text
          size="sm"
          fontWeight="$semibold"
          textAlign="center"
          color="$textLight900"
          $dark-color="$textDark100"
        >
          {achievement.title}
        </Text>
        
        <Text
          size="xs"
          textAlign="center"
          color="$textLight600"
          $dark-color="$textDark400"
          numberOfLines={2}
        >
          {achievement.description}
        </Text>
        
        <Text
          size="xs"
          color={isUnlocked ? "$success600" : "$textLight500"}
          $dark-color={isUnlocked ? "$success400" : "$textDark500"}
          fontWeight="$medium"
        >
          +{achievement.points} 积分
        </Text>
      </VStack>
    </Box>
  );
}
