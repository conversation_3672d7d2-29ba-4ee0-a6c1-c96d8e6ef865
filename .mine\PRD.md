# Supaplayer 应用 - MVP 产品需求文档

**核心理念：** Supaplayer 是一款以 “AI 辅助生成 Todo List” 为核心，结合 “简单好玩” 的 “游戏化” 体验的任务管理应用。我们致力于让用户通过 AI 高效创建和管理待办事项，并通过轻量级的游戏化机制获得乐趣和动力。

**MVP 目标：**

*   **AI 核心功能:** 用户能够通过简单的交互，让 AI 帮助生成、拆解 Todo List。
*   **核心游戏化:** 用户在完成 AI 生成的任务时，能获得即时反馈和奖励（积分、连胜）。
*   **简单易用:** 提供清爽、直观的用户界面，降低用户学习成本，实现沉浸式单屏核心体验。

**MVP 核心功能与游戏化元素：**

1.  **AI 辅助 Todo List 生成 (核心)**
    *   **功能描述:** 用户输入任务主题或想法，AI 辅助生成具体的、可执行的待办事项列表。
    *   **交互:** 在主界面“Supaplayer 指挥中心”内提供简洁的输入界面，AI 快速响应并直接在指挥中心的“当前任务列表区”展示生成的列表。
    *   **实现位置:** 主要在“Supaplayer 指挥中心”主界面的“AI 任务生成区”和“当前任务列表区”。

2.  **积分系统 (Points System - 核心游戏化)**
    *   **定义:** 用户完成 AI 生成的 Todo List 任务后获得积分，作为衡量活跃度和成就感的基础指标。
    *   **获取途径:**
        *   完成一个 AI 生成的任务：获得固定或根据任务复杂度（可选，MVP 初期可固定）计算的积分。
    *   **用途 (MVP):**
        *   主要用于即时反馈和成就感体现。
        *   （可选，视开发复杂度）解锁简单的视觉定制元素（如主题色），通过“我的 Supaplayer 统计”面板进行设置。
    *   **展示:**
        *   任务完成时在“当前任务列表区”即时提示获得积分。
        *   “Supaplayer 指挥中心”主界面顶部的“状态区”实时展示总积分摘要。
        *   完整的积分详情通过“快捷指令面板”访问的“我的 Supaplayer 统计”面板展示。

3.  **连胜奖励 (Streak Rewards - 核心游戏化)**
    *   **定义:** 用户连续多天完成至少一个 AI 生成的任务，即可维持并增长连胜天数，获得奖励。
    *   **机制:**
        *   每日完成至少一项 AI 生成的任务，连胜天数 +1。
        *   若某日未完成任何 AI 生成的任务，连胜中断。
    *   **奖励:**
        *   达到特定连胜天数（如3天、7天、14天）时，给予额外的积分奖励。
    *   **展示:**
        *   “Supaplayer 指挥中心”主界面顶部的“状态区”清晰展示当前连胜天数。
        *   “我的 Supaplayer 统计”面板中也会展示当前连胜天数。
        *   达成连胜里程碑时给予祝贺提示。

4.  **简单成就 (Basic Achievements - 辅助游戏化)**
    *   **定义:** 针对核心 AI 任务行为设定少量易于理解和达成的成就，给予用户里程碑式的认可。
    *   **MVP 成就示例:**
        *   “AI 初探者”：首次成功使用 AI 生成 Todo List。
        *   “任务执行者”：累计完成 10 个 AI 生成的任务。
        *   “连胜新星”：首次达成 3 天连胜。
    *   **奖励:**
        *   少量额外积分。
        *   （可选）简单的徽章图标展示。
    *   **展示:**
        *   “Supaplayer 指挥中心”主界面集成“成就与进度概览区”，展示最近解锁和进行中的成就摘要。
        *   完整的已获得成就列表及徽章，通过“快捷指令面板”访问的“我的 Supaplayer 统计”面板展示。
        *   达成成就时弹出提示。

**MVP 页面规划与导航结构：**

*   **主界面：“Supaplayer 指挥中心” (Supaplayer Command Center - 核心单屏体验)**
    *   **定位：** 应用启动后的唯一主屏幕，承载核心用户旅程，动态展示用户状态与任务。
    *   **核心区域与元素 (自上而下、可滚动布局)：**
        *   **顶部状态区 (Hero Section)：**
            *   个性化问候语及当前连胜状态。
            *   关键统计数据醒目展示：总积分、当前连胜天数。
        *   **AI 任务生成区 (“AI 任务控制台”):**
            *   清晰的行动召唤 (CTA) 按钮，引导用户使用 AI 生成 Todo List (例如：“AI 规划新任务”或“+ AI 创建清单”)。
            *   简洁的任务主题输入界面，AI 处理时有明确的视觉反馈。
        *   **当前任务列表区 (“行动清单”):**
            *   展示由 AI 生成的（或用户手动添加的）待办事项，每项任务清晰易读，有明确的完成交互（如复选框）。
            *   支持任务完成标记，并提供即时反馈（如任务划掉、积分增加动画、音效可选）。
            *   空状态时，友好提示引导用户创建新任务。
        *   **成就与进度概览区：**
            *   展示最近1-2个已解锁的成就及其图标。
            *   可视化展示1-2个用户接近达成的、进行中的关键成就进度条或提示。
*   **全局导航：“快捷指令面板” (Floating Quick Access Panel)**
    *   **定位与形式：** 固定于“指挥中心”屏幕右下角的浮动操作按钮 (FAB)。初始状态为单个主 FAB 图标（例如：菜单图标或品牌相关简约图标）。
    *   **交互：**
        *   点击主 FAB，向上方或左方平滑展开，显示“我的 Supaplayer 统计”和“应用设置”的子图标按钮。
        *   再次点击主 FAB 或点击面板以外的屏幕区域可收起该面板。
        *   展开和收起过程有流畅、积极的动画反馈。
*   **二级信息呈现 (通过“快捷指令面板”访问)：**
    *   **“我的 Supaplayer 统计”面板 (取代原 Roles 页面概念)：**
        *   **触发：** 点击“快捷指令面板”中代表个人统计/资料的子图标。
        *   **呈现：** 以覆盖式面板 (Overlay Panel) 形式从屏幕侧边或底部平滑滑出，不完全遮挡主指挥中心背景（或有半透明背景）。
        *   **内容：**
            *   用户昵称/头像（极简版）。
            *   用户总积分详细数字。
            *   完整的已获得成就列表及徽章展示。
            *   当前连胜天数。
            *   （可选 MVP）简单的视觉主题切换入口（如果积分可用于解锁）。
        *   **关闭：** 提供明确的关闭按钮（如“X”或“完成”）或支持手势关闭。
    *   **“应用设置”面板：**
        *   **触发：** 点击“快捷指令面板”中代表设置的子图标（如齿轮图标）。
        *   **呈现：** 以覆盖式面板或模态视图 (Modal) 形式出现。
        *   **内容：** 应用基础设置（如通知开关、账户管理入口、关于应用、反馈等，MVP 阶段极简）。
        *   **关闭：** 提供明确的关闭按钮。

**移除或延后的功能 (非 MVP):**

*   复杂的排行榜系统。
*   虚拟货币系统。
*   AI 助手个性化、3D 角色装扮。
*   社区功能、任务模版分享。
*   高级 AI 功能（如深度分析、定制规划模型等）。
*   复杂的每日/每周 AI 任务包。
*   过于细致的积分获取规则和用途。
*   大量、多层级的成就系统。
*   手动添加、编辑、排序非 AI 生成的复杂任务功能。

**设计与体验侧重：**

*   **沉浸式单屏体验：** 核心功能和信息流高度整合在“指挥中心”主界面，减少页面跳转。
*   **简洁直观：** 界面清爽，操作流程简单明了，图标和行动召唤清晰易懂。
*   **即时反馈：** 用户完成任务、获得积分、达成连胜或成就时，有积极、愉悦的视觉和情感反馈（如微动画、祝贺信息）。
*   **趣味性：** 通过轻松的动画、音效（可选）和鼓励性文案，提升使用乐趣。
*   **AI 融入自然：** AI 作为核心助手自然地嵌入任务创建流程，而不是一个孤立的功能。

**技术栈遵循：** Expo SDK, TypeScript, React Native, Expo Router, Zustand, Gluestack UI v2, i18next。
