import { create } from 'zustand';
import { User } from '@supabase/supabase-js';
import { supabase } from '../supabase';
import { SupabaseService } from '../services/supabase-service';

interface AuthState {
  user: User | null;
  loading: boolean;
  initialized: boolean;
  signUp: (email: string, password: string, fullName?: string) => Promise<void>;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  initialize: () => Promise<void>;
}

export const useAuthStore = create<AuthState>((set, get) => ({
  user: null,
  loading: false,
  initialized: false,

  signUp: async (email, password, fullName) => {
    set({ loading: true });
    try {
      await SupabaseService.signUp(email, password, fullName);
      // User will be set through the auth state change listener
    } catch (error) {
      console.error('Sign up error:', error);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  signIn: async (email, password) => {
    set({ loading: true });
    try {
      const { user } = await SupabaseService.signIn(email, password);
      set({ user });
    } catch (error) {
      console.error('Sign in error:', error);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  signOut: async () => {
    set({ loading: true });
    try {
      await SupabaseService.signOut();
      set({ user: null });
    } catch (error) {
      console.error('Sign out error:', error);
      throw error;
    } finally {
      set({ loading: false });
    }
  },

  initialize: async () => {
    try {
      // Get initial session
      const { data: { session } } = await supabase.auth.getSession();
      set({ user: session?.user ?? null });

      // Listen for auth changes
      supabase.auth.onAuthStateChange(async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email);
        set({ user: session?.user ?? null });
        
        if (event === 'SIGNED_IN' && session?.user) {
          // Sync user data when signed in
          try {
            const profile = await SupabaseService.getProfile();
            if (profile) {
              // Update user store with profile data
              const { useUserStore } = await import('./user-store');
              useUserStore.getState().syncFromDatabase(profile);
            }
          } catch (error) {
            console.error('Failed to sync profile:', error);
          }
        }
      });

      set({ initialized: true });
    } catch (error) {
      console.error('Auth initialization error:', error);
      set({ initialized: true });
    }
  },
}));
